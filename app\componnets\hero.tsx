"use client"
import React, { useState, useEffect } from "react";

const FiChevronLeft = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
    <polyline points="15 18 9 12 15 6"></polyline>
  </svg>
);

const FiChevronRight = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
    <polyline points="9 18 15 12 9 6"></polyline>
  </svg>
);

const FiBriefcase = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
    <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
    <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
  </svg>
);

const FiFilm = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
        <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect><line x1="7" y1="2" x2="7" y2="22"></line><line x1="17" y1="2" x2="17" y2="22"></line><line x1="2" y1="12" x2="22" y2="12"></line><line x1="2" y1="7" x2="7" y2="7"></line><line x1="2" y1="17" x2="7" y2="17"></line><line x1="17" y1="17" x2="22" y2="17"></line><line x1="17" y1="7" x2="22" y2="7"></line>
    </svg>
);

const FiCoffee = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
        <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line>
    </svg>
);

const FiUsers = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
    </svg>
);

const FiStar = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
    </svg>
);

const FiHeart = () => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-lg sm:text-xl md:text-2xl text-white">
        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </svg>
);

// Background images for the slider
const backgroundImages = [
  "https://images.pexels.com/photos/12887817/pexels-photo-12887817.jpeg",
  "https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg",
  "https://images.pexels.com/photos/2422915/pexels-photo-2422915.jpeg",
  "https://images.pexels.com/photos/1181533/pexels-photo-1181533.jpeg",
  "https://images.pexels.com/photos/2422290/pexels-photo-2422290.jpeg"
];

const categories = [
  { label: "Business & Industry", icon: FiBriefcase },
  { label: "Entertainment", icon: FiFilm },
  { label: "Food & Drink", icon: FiCoffee },
  { label: "Family & Relationships", icon: FiUsers },
  { label: "Hobbies & Activities", icon: FiStar },
  { label: "Fitness & Wellness", icon: FiHeart },
];

function Category({ label, Icon }: { label: string; Icon: React.ComponentType }) {
  return (
    <div className="flex w-full max-w-[140px] sm:max-w-[160px] md:max-w-[180px] flex-col items-center gap-2 sm:gap-3 text-center text-white">
      <div className="grid h-12 w-12 sm:h-14 sm:w-14 md:h-16 md:w-16 place-items-center rounded-full bg-white/15 ring-1 ring-white/30 backdrop-blur-md">
        <Icon />
      </div>
      <p className="text-xs sm:text-sm md:text-xs leading-tight text-white/90 px-1">{label}</p>
    </div>
  );
}

function Hero() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentImageIndex]);

  const handlePrevious = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentImageIndex((prev) =>
      prev === 0 ? backgroundImages.length - 1 : prev - 1
    );
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const handleNext = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentImageIndex((prev) =>
      prev === backgroundImages.length - 1 ? 0 : prev + 1
    );
    setTimeout(() => setIsTransitioning(false), 500);
  };

  return (
    <section className="relative min-h-screen w-full bg-black overflow-hidden">
      {backgroundImages.map((image, index) => (
        <div
          key={index}
          className={`absolute inset-0 bg-cover bg-center transition-opacity duration-500 ease-in-out ${
            index === currentImageIndex ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ backgroundImage: `url('${image}')` }}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70" />
        </div>
      ))}

      <button
        onClick={handlePrevious}
        disabled={isTransitioning}
        aria-label="Previous"
        className="group absolute left-2 sm:left-4 md:left-6 lg:left-8 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white/80 p-2 sm:p-3 text-gray-800 shadow hover:bg-white disabled:opacity-50 transition-all duration-200 hover:scale-105"
      >
        <FiChevronLeft />
      </button>
      <button
        onClick={handleNext}
        disabled={isTransitioning}
        aria-label="Next"
        className="group absolute right-2 sm:right-4 md:right-6 lg:right-8 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white/80 p-2 sm:p-3 text-gray-800 shadow hover:bg-white disabled:opacity-50 transition-all duration-200 hover:scale-105"
      >
        <FiChevronRight />
      </button>

      <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 z-10 flex -translate-x-1/2 gap-1 sm:gap-2">
        {backgroundImages.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              if (!isTransitioning) {
                setIsTransitioning(true);
                setCurrentImageIndex(index);
                setTimeout(() => setIsTransitioning(false), 500);
              }
            }}
            className={`h-1.5 w-6 sm:h-2 sm:w-8 rounded-full transition-all duration-300 ${
              index === currentImageIndex
                ? 'bg-white'
                : 'bg-white/40 hover:bg-white/60'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      <div className="relative z-10 mx-auto flex min-h-screen max-w-7xl flex-col items-center justify-center px-3 sm:px-4 md:px-6 lg:px-8">
        <div className="w-full max-w-xs sm:max-w-2xl md:max-w-3xl lg:max-w-4xl overflow-hidden rounded-md bg-white/20 text-center font-sans text-lg sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl uppercase tracking-wide text-white backdrop-blur-xl ring-1 ring-white/30">
            <div className="p-3 sm:p-4 md:p-6 border-b border-white/30 font-normal">
                Going alone is great.
            </div>
            <div className="p-4 sm:p-6 md:p-8 font-bold">
                Going together is{" "}
                <span className="bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
                  better.
                </span>
            </div>
        </div>

        <p className="mt-4 sm:mt-6 md:mt-8 max-w-xs sm:max-w-lg md:max-w-2xl lg:max-w-3xl text-center text-xs sm:text-sm md:text-base text-white/90 px-2">
          Find a reliable companion for your next adventure—<br className="hidden sm:block" />
          <span className="sm:hidden"> </span>with a unique feature that combats ghosting.
        </p>

        <button className="mt-4 sm:mt-6 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-semibold text-white shadow-md drop-shadow-[0_5px_15px_rgba(8,145,178,0.3)] hover:brightness-110 transition-all duration-200">
          Join the Community Today
        </button>

        <div className="mt-6 sm:mt-8 md:mt-10 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-x-2 sm:gap-x-4 md:gap-x-6 gap-y-4 sm:gap-y-6 md:gap-y-8 justify-items-center">
          {categories.map((c) => (
            <Category key={c.label} label={c.label} Icon={c.icon} />
          ))}
        </div>

        <div className="mt-6 sm:mt-8 md:mt-10">
          <div className="flex items-center gap-2 rounded-full border border-white/30 bg-black/30 px-3 sm:px-4 md:px-6 py-2 sm:py-3 text-xs sm:text-sm text-white/90 backdrop-blur-md">
            <span className="text-white/70">Activities in</span>
            <a href="#" className="font-semibold text-blue-400 underline underline-offset-4">
              Calgary
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}

export default Hero