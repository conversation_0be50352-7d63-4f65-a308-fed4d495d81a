"use client"

import Image from "next/image";
import { useState } from "react";

const FiLogIn = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5">
    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
    <polyline points="10 17 15 12 10 7"></polyline>
    <line x1="15" y1="12" x2="3" y2="12"></line>
  </svg>
);

const FiMenu = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
    <line x1="3" y1="6" x2="21" y2="6"></line>
    <line x1="3" y1="12" x2="21" y2="12"></line>
    <line x1="3" y1="18" x2="21" y2="18"></line>
  </svg>
);

const FiX = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);



function NavBar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="absolute top-0 left-0 right-0 z-20">
      <div className="mx-auto flex max-w-7xl items-center justify-between px-3 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4 lg:py-5">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <Image
            src="/logo.png"
            alt="Logo"
            width={170}
            height={170}
          />
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden items-center gap-3 md:gap-4 lg:gap-6 xl:gap-8 md:flex">
          <a href="#" className="text-xs md:text-sm lg:text-base font-medium text-white/90 hover:text-white transition-colors duration-200 whitespace-nowrap">About</a>
          <a href="#" className="text-xs md:text-sm lg:text-base font-medium text-white/90 hover:text-white transition-colors duration-200 whitespace-nowrap">Features</a>
          <a href="#" className="text-xs md:text-sm lg:text-base font-medium text-white/90 hover:text-white transition-colors duration-200 whitespace-nowrap">How it Works</a>
          <button className="inline-flex items-center gap-1 md:gap-2 rounded-tl-2xl rounded-br-2xl bg-gradient-to-r from-blue-500 to-teal-400 px-2 md:px-3 lg:px-4 py-1.5 md:py-2 text-xs md:text-sm lg:text-base font-semibold text-white shadow-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-200 whitespace-nowrap">
            <FiLogIn /> Login
          </button>
        </nav>

        {/* Mobile Menu Button */}
        <button
          onClick={toggleMenu}
          className="md:hidden rounded-lg bg-white/10 p-1.5 sm:p-2 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/20"
          aria-label="Toggle menu"
        >
          {isMenuOpen ? <FiX /> : <FiMenu />}
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-200 md:hidden shadow-lg">
          <nav className="flex flex-col px-3 sm:px-4 py-4 sm:py-6 space-y-3 sm:space-y-4">
            <a
              href="#"
              className="text-sm sm:text-base font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200 py-2 border-b border-gray-200"
              onClick={() => setIsMenuOpen(false)}
            >
              About
            </a>
            <a
              href="#"
              className="text-sm sm:text-base font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200 py-2 border-b border-gray-200"
              onClick={() => setIsMenuOpen(false)}
            >
              Features
            </a>
            <a
              href="#"
              className="text-sm sm:text-base font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200 py-2 border-b border-gray-200"
              onClick={() => setIsMenuOpen(false)}
            >
              How it Works
            </a>
            <button
              className="inline-flex items-center justify-center gap-2 rounded-tl-2xl rounded-br-2xl bg-gradient-to-r from-blue-500 to-teal-400 px-4 py-2.5 sm:py-3 text-sm sm:text-base font-semibold text-white shadow-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-200 mt-2 sm:mt-4"
              onClick={() => setIsMenuOpen(false)}
            >
              <FiLogIn /> Login
            </button>
          </nav>
        </div>
      )}
    </header>
  );
}

export default NavBar;
