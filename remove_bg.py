#!/usr/bin/env python3
"""
Script to remove white background from an image and save as PNG with transparency.
"""

from PIL import Image
import numpy as np
import sys
import os

def remove_white_background(input_path, output_path, threshold=240):
    """
    Remove white background from an image.
    
    Args:
        input_path (str): Path to input image
        output_path (str): Path to save output image
        threshold (int): Threshold for considering a pixel as white (0-255)
    """
    try:
        # Open the image
        img = Image.open(input_path)
        
        # Convert to RGBA if not already
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Convert to numpy array
        data = np.array(img)
        
        # Create a mask for white pixels
        # A pixel is considered white if all RGB values are above the threshold
        white_mask = (data[:, :, 0] >= threshold) & \
                     (data[:, :, 1] >= threshold) & \
                     (data[:, :, 2] >= threshold)
        
        # Set alpha channel to 0 (transparent) for white pixels
        data[white_mask, 3] = 0
        
        # Create new image from modified data
        result_img = Image.fromarray(data, 'RGBA')
        
        # Save the result
        result_img.save(output_path, 'PNG')
        print(f"✅ Background removed successfully!")
        print(f"📁 Saved to: {output_path}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def main():
    input_file = "public/logo.png"
    output_file = "public/logo_no_bg.png"
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return
    
    print(f"🔄 Processing {input_file}...")
    
    # Try different thresholds if the first one doesn't work well
    thresholds = [240, 220, 200, 180]
    
    for i, threshold in enumerate(thresholds):
        current_output = output_file if i == 0 else f"public/logo_no_bg_t{threshold}.png"
        print(f"🎯 Trying threshold {threshold}...")
        
        if remove_white_background(input_file, current_output, threshold):
            print(f"✨ Created: {current_output}")
        
        if i == 0:  # Always create the main output file
            break

if __name__ == "__main__":
    main()
